import json

def read_jsonl_file(file_path):
    """
    读取JSONL文件并打印内容
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            print(f"正在读取文件: {file_path}")
            print("=" * 80)
            
            for line_num, line in enumerate(file, 1):
                line = line.strip()
                if line:  # 跳过空行
                    try:
                        # 解析JSON行
                        data = json.loads(line)
                        
                        print(f"\n--- 记录 {line_num} ---")
                        print(f"Prompt: {data.get('prompt', 'N/A')[:100]}...")
                        print(f"Predict长度: {len(data.get('predict', ''))}")
                        print(f"Label长度: {len(data.get('label', ''))}")
                        
                        # 如果需要查看完整内容，可以取消下面的注释
                        # print(f"完整Predict: {data.get('predict', 'N/A')}")
                        # print(f"完整Label: {data.get('label', 'N/A')}")
                        
                    except json.JSONDecodeError as e:
                        print(f"第 {line_num} 行JSON解析错误: {e}")
                        print(f"原始内容: {line[:200]}...")
            
            print(f"\n文件读取完成，共处理 {line_num} 行")
            
    except FileNotFoundError:
        print(f"文件未找到: {file_path}")
    except Exception as e:
        print(f"读取文件时发生错误: {e}")

def read_and_convert_to_json(file_path, output_path=None):
    """
    读取JSONL文件并转换为标准JSON格式
    """
    data_list = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()
                if line:
                    try:
                        data = json.loads(line)
                        data['id'] = line_num  # 添加ID
                        data_list.append(data)
                    except json.JSONDecodeError as e:
                        print(f"第 {line_num} 行JSON解析错误: {e}")
        
        # 创建标准JSON结构
        result = {
            "total_records": len(data_list),
            "predictions": data_list
        }
        
        # 如果指定了输出路径，保存到文件
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as output_file:
                json.dump(result, output_file, ensure_ascii=False, indent=2)
            print(f"已保存为标准JSON格式到: {output_path}")
        
        return result
        
    except Exception as e:
        print(f"转换过程中发生错误: {e}")
        return None

if __name__ == "__main__":
    # 读取并打印JSONL文件内容
    file_path = "generated_predictions.jsonl"
    
    print("方式1: 简要查看文件内容")
    read_jsonl_file(file_path)
    
    print("\n" + "=" * 80)
    print("方式2: 转换为标准JSON格式")
    
    # 转换为标准JSON并保存
    result = read_and_convert_to_json(file_path, "generated_predictions_formatted.json")
    
    if result:
        print(f"\n转换成功！共有 {result['total_records']} 条记录")
        print("\n前3条记录的简要信息:")
        for i, record in enumerate(result['predictions'][:3]):
            print(f"记录 {i+1}:")
            print(f"  - ID: {record.get('id')}")
            print(f"  - Prompt长度: {len(record.get('prompt', ''))}")
            print(f"  - Predict长度: {len(record.get('predict', ''))}")
            print(f"  - Label长度: {len(record.get('label', ''))}")
