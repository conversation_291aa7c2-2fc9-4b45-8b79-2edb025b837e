import torch
import torch.nn as nn


class LSTMRULPredictor(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers, output_size, dropout=0.2):
        super(LSTMRULPredictor, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True, dropout=dropout)
        self.dropout = nn.Dropout(dropout)
        self.fc = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        # x shape: (batch_size, window_size, input_size)
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)

        out, _ = self.lstm(x, (h0, c0))  # out shape: (batch_size, seq_length, hidden_size)

        # 我们只取最后一个时间步的输出
        out = out[:, -1, :]
        out = self.dropout(out)
        out = self.fc(out)
        return out


# 初始化模型
# input_size: 特征数量 (e.g., 21个传感器 + 3个设置 = 24)
# output_size: 1 (预测一个RUL值)
model = LSTMRULPredictor(input_size=24, hidden_size=64, num_layers=2, output_size=1)