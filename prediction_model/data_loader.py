import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import torch
from torch.utils.data import Dataset, DataLoader
import joblib


class CMAPSSDataset(Dataset):
    def __init__(self, data_path, subset='FD001', mode='train', window_size=30, step_size=1, max_rul=125):
        """
        加载NASA C-MAPSS数据集
        :param data_path: 数据目录路径
        :param subset: 子集名称 (FD001, FD002, FD003, FD004)
        :param mode: 'train' 或 'test'
        :param window_size: 滑动窗口大小
        :param step_size: 滑动步长
        """
        self.window_size = window_size
        self.step_size = step_size
        self.max_rul = max_rul  # FD001的最大RUL

        # 读取数据
        if mode == 'train':
            data_file = f'{data_path}/train_{subset}.txt'
            df = pd.read_csv(data_file, sep='\s+', header=None)
        else:
            data_file = f'{data_path}/test_{subset}.txt'
            df = pd.read_csv(data_file, sep='\s+', header=None)
            # 对于测试集，还需要读取RUL标签
            rul_file = f'{data_path}/RUL_{subset}.txt'
            true_rul = pd.read_csv(rul_file, sep='\s+', header=None)

        # 列名 (根据C-MAPSS数据文档)
        columns = ['unit', 'cycle'] + [f'op_setting_{i}' for i in range(1, 4)] + [f'sensor_{i}' for i in range(1, 22)]
        df.columns = columns

        # 删除恒定不变的传感器
        df.drop(columns=['sensor_1', 'sensor_5', 'sensor_6', 'sensor_10', 'sensor_16', 'sensor_18', 'sensor_19'],
                inplace=True)

        # 计算每个发动机的最大周期
        max_cycle = df.groupby('unit')['cycle'].max().reset_index()
        max_cycle.columns = ['unit', 'max_cycle']

        # 合并数据
        df = df.merge(max_cycle, on='unit', how='left')


        # 计算RUL (剩余使用寿命)，并限制在0到max_rul之间
        df['RUL'] = df['max_cycle'] - df['cycle']
        df['RUL'] = df['RUL'].clip(upper=self.max_rul) # 裁剪RUL

        # 选择特征列
        feature_columns = [col for col in df.columns if col not in ['unit', 'cycle', 'max_cycle', 'RUL']]

        # 标准化特征
        if mode == 'train':
            # 仅使用训练数据拟合scaler
            self.scaler = StandardScaler()
            df[feature_columns] = self.scaler.fit_transform(df[feature_columns])
            # 保存scaler用于测试集
            joblib.dump(self.scaler, f'{data_path}/scaler_{subset}.pkl')
        else:
            # 加载训练集的scaler
            self.scaler = joblib.load(f'{data_path}/scaler_{subset}.pkl')
            df[feature_columns] = self.scaler.transform(df[feature_columns])

        # 为每个发动机构建序列
        self.sequences = []
        self.labels = []

        units = df['unit'].unique()
        for unit in units:
            unit_data = df[df['unit'] == unit]
            unit_features = unit_data[feature_columns].values
            unit_rul = unit_data['RUL'].values

            # 创建滑动窗口
            for i in range(0, len(unit_data) - window_size, step_size):
                self.sequences.append(unit_features[i:i + window_size])
                self.labels.append(unit_rul[i + window_size - 1])  # 取窗口最后一个时间点的RUL

        self.sequences = np.array(self.sequences)
        self.labels = np.array(self.labels)

        print(f"Loaded {len(self.sequences)} sequences from {subset} {mode} set")

    def __len__(self):
        return len(self.sequences)

    def __getitem__(self, idx):
        return torch.FloatTensor(self.sequences[idx]), torch.FloatTensor([self.labels[idx]])


def get_data_loaders(data_path, subset='FD001', batch_size=32, window_size=30):
    """创建训练和验证数据加载器"""
    # 创建完整训练集
    full_dataset = CMAPSSDataset(data_path, subset=subset, mode='train', window_size=window_size)

    # 分割训练集和验证集
    train_size = int(0.8 * len(full_dataset))
    val_size = len(full_dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(full_dataset, [train_size, val_size])

    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)

    return train_loader, val_loader


def get_test_loader(data_path, subset='FD001', batch_size=32, window_size=30):
    """创建测试数据加载器"""
    test_dataset = CMAPSSDataset(data_path, subset=subset, mode='test', window_size=window_size)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    return test_loader