import torch
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, mean_absolute_error
from data_loader import get_test_loader
from LSTM_models import LSTMRULPredictor


def calculate_score(y_true, y_pred):
    """计算NASA的评分函数"""
    d = y_pred - y_true
    d_clipped = np.clip(d, -10, 100)
    score = np.sum(np.where(d_clipped < 0, np.exp(-d_clipped / 13) - 1, np.exp(d_clipped / 10) - 1))
    return score


def evaluate_model(data_path, model_path, subset='FD001', window_size=30, hidden_size=64, num_layers=2):
    """评估模型性能"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # 加载测试数据
    test_loader = get_test_loader(data_path, subset, batch_size=1, window_size=window_size)

    # 初始化模型
    input_size = test_loader.dataset[0][0].shape[1]  # 特征数量
    model = LSTMRULPredictor(input_size, hidden_size, num_layers, output_size=1)
    model.to(device)

    # 加载训练好的模型
    checkpoint = torch.load(model_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()

    # 进行预测
    predictions = []
    actuals = []

    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)

            predictions.append(output.cpu().numpy()[0][0])
            actuals.append(target.cpu().numpy()[0][0])

    # 计算评估指标
    rmse = np.sqrt(mean_squared_error(actuals, predictions))
    mae = mean_absolute_error(actuals, predictions)
    score = calculate_score(np.array(actuals), np.array(predictions))

    print(f"RMSE: {rmse:.4f}")
    print(f"MAE: {mae:.4f}")
    print(f"Score: {score:.4f}")

    # 绘制预测结果对比图
    plt.figure(figsize=(12, 6))
    plt.plot(actuals, label='Actual RUL')
    plt.plot(predictions, label='Predicted RUL')
    plt.xlabel('Sample Index')
    plt.ylabel('RUL (cycles)')
    plt.title('RUL Prediction Results')
    plt.legend()
    plt.savefig('rul_prediction_results.png')
    plt.show()

    # 在evaluate函数中添加可视化
    plt.figure(figsize=(10, 6))
    plt.scatter(actuals, predictions, alpha=0.5)
    plt.plot([0, max(actuals)], [0, max(actuals)], 'r--')
    plt.xlabel('True RUL')
    plt.ylabel('Predicted RUL')
    plt.title('True vs Predicted RUL')
    plt.savefig('true_vs_predicted.png')
    plt.show()

    return rmse, mae, score


if __name__ == "__main__":
    data_path = "./data/C-MAPSS-Data"
    model_path = "./runs/exp/best_model.pth"
    evaluate_model(data_path, model_path)