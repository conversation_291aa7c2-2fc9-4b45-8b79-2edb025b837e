H       ��H�	��g�Z1�A
brain.Event:2R.
,tensorboard.summary.writer.event_file_writer޼t       ��2	�8�Z1�A*


Loss/trainɝ�Er��n       ���	�8�Z1�A*

Loss/val���Et��!       {��	�8�Z1�A*


Learning Rateo�:�S        )�P	����Z1�A*


Loss/trainc�CE,�7       ��2	����Z1�A*

Loss/val�UE��.�#       ��wC	����Z1�A*


Learning Rateo�:-��J        )�P	����Z1�A*


Loss/trainv��D�fY       ��2	����Z1�A*

Loss/val�W�D?>�#       ��wC	����Z1�A*


Learning Rateo�:n�|s        )�P	�&]�Z1�A*


Loss/train-iD�0?       ��2	�&]�Z1�A*

Loss/val�L'D�&�#       ��wC	�&]�Z1�A*


Learning Rateo�:M���        )�P	�a��Z1�A*


Loss/train�zD���       ��2	�a��Z1�A*

Loss/valy��C�C��#       ��wC	�a��Z1�A*


Learning Rateo�:�`        )�P	ٍ4�Z1�A*


Loss/train��C:u�4       ��2	ٍ4�Z1�A*

Loss/valJ�C�Q#       ��wC	ٍ4�Z1�A*


Learning Rateo�:<��8        )�P	X���Z1�A*


Loss/traine�}C�GM       ��2	X���Z1�A*

Loss/val��<CZ�=�#       ��wC	X���Z1�A*


Learning Rateo�:e6��        )�P	wT�Z1�A*


Loss/traingRCsT��       ��2	wT�Z1�A*

Loss/val�C�S�#       ��wC	wT�Z1�A*


Learning Rateo�:5{�        )�P	��z�Z1�A*


Loss/trainio2CyZ�t       ��2	��z�Z1�A*

Loss/valZlC���#       ��wC	��z�Z1�A*


Learning Rateo�:�~o�        )�P	���Z1�A	*


Loss/train`#CwB�+       ��2	���Z1�A	*

Loss/valF�B��3.#       ��wC	���Z1�A	*


Learning Rateo�:4c?        )�P	�:R�Z1�A
*


Loss/trainҜC\�F�       ��2	�:R�Z1�A
*

Loss/val���B�r��#       ��wC	�:R�Z1�A
*


Learning Rateo�:!��        )�P	�r��Z1�A*


Loss/train+�Cş�C       ��2	�r��Z1�A*

Loss/val��B&�~#       ��wC	�r��Z1�A*


Learning Rateo�:ȗ        )�P	��2�Z1�A*


Loss/train�-�B���       ��2	��2�Z1�A*

Loss/val�ƞB���e#       ��wC	��2�Z1�A*


Learning Rateo�:�[�        )�P	���Z1�A
*


Loss/train��B���6       ��2	���Z1�A
*

Loss/val�z�B�� #       ��wC	���Z1�A
*


Learning Rateo�:V��E        )�P	$�Z1�A*


Loss/train��BFb       ��2	$�Z1�A*

Loss/val=��B��l#       ��wC	$�Z1�A*


Learning Rateo�:�a�v        )�P	���Z1�A*


Loss/train�E�B;9�       ��2	���Z1�A*

Loss/vala$~B|�#       ��wC	���Z1�A*


Learning Rateo�:{�Ə        )�P	��	�Z1�A*


Loss/train�B�)�Y       ��2	��	�Z1�A*

Loss/val[�B�pw#       ��wC	��	�Z1�A*


Learning Rateo�:	��        )�P	↍�Z1�A*


Loss/train��B��#�       ��2	↍�Z1�A*

Loss/val�HgBukc�#       ��wC	↍�Z1�A*


Learning Rateo�:�E��        )�P	
��Z1�A*


Loss/train�@�BX��N       ��2	
��Z1�A*

Loss/val0�9BZ>�#       ��wC	
��Z1�A*


Learning Rateo�:����        )�P	�kt�Z1�A*


Loss/train�1�B�u       ��2	�kt�Z1�A*

Loss/vala�CBA #       ��wC	�kt�Z1�A*


Learning Rateo�:��F        )�P	�7��Z1�A*


Loss/train��B��       ��2	�7��Z1�A*

Loss/val �HB�� �#       ��wC	�7��Z1�A*


Learning Rateo�:�n��        )�P	�M�Z1�A*


Loss/trainw�B
+ͫ       ��2	�M�Z1�A*

Loss/val�5By�M+#       ��wC	�M�Z1�A*


Learning Rateo�:�'C�        )�P	�ֹ�Z1�A*


Loss/train�ŋB�P�D       ��2	:��Z1�A*

Loss/valcSYB���#       ��wC	:��Z1�A*


Learning Rateo�:#�        )�P	%�$�Z1�A*


Loss/train<?�B�A#       ��2	%�$�Z1�A*

Loss/val�aBҹ%#       ��wC	%�$�Z1�A*


Learning Rateo�:f�        )�P	����Z1�A*


Loss/train�B��3       ��2	����Z1�A*

Loss/val��B�y�(#       ��wC	����Z1�A*


Learning Rateo�:�w��        )�P	����Z1�A*


Loss/train
�B ��4       ��2	����Z1�A*

Loss/val���A�@Z1#       ��wC	����Z1�A*


Learning Rateo�:��        )�P	�Mo�Z1�A*


Loss/train�)qB����       ��2	�Mo�Z1�A*

Loss/val���A����#       ��wC	�Mo�Z1�A*


Learning Rateo�:���        )�P	���Z1�A*


Loss/train�mBniz       ��2	���Z1�A*

Loss/val+�A�
�M#       ��wC	���Z1�A*


Learning Rateo�:`��|        )�P	�lQ�Z1�A*


Loss/train�SaBU��/       ��2	�lQ�Z1�A*

Loss/val�p�AH"E�#       ��wC	�lQ�Z1�A*


Learning Rateo�:��i        )�P	 ���Z1�A*


Loss/trainGv[Be�(�       ��2	 ���Z1�A*

Loss/val�a�A�H�#       ��wC	 ���Z1�A*


Learning Rateo�:��y        )�P	1
+�Z1�A*


Loss/train��UB��       ��2	1
+�Z1�A*

Loss/val�ɤA��?#       ��wC	1
+�Z1�A*


Learning Rateo�:jd�"        )�P	����Z1�A*


Loss/train?aUB?c�*       ��2	U���Z1�A*

Loss/val��A��(#       ��wC	U���Z1�A*


Learning Rateo�:��R        )�P	@��Z1�A *


Loss/train�.BB�7W�       ��2	@��Z1�A *

Loss/val�|�Ab�#       ��wC	@��Z1�A *


Learning Rateo�:�o)        )�P	�?��Z1�A!*


Loss/train�IB��f       ��2	�?��Z1�A!*

Loss/vallڡA^�
�#       ��wC	�?��Z1�A!*


Learning Rateo�:
k��        )�P	G���Z1�A"*


Loss/traint�DBrug       ��2	G���Z1�A"*

Loss/val�A��Z#       ��wC	G���Z1�A"*


Learning Rateo�:"��        )�P	��Z�Z1�A#*


Loss/train�NKBpz�K       ��2	��Z�Z1�A#*

Loss/val͚�A����#       ��wC	��Z�Z1�A#*


Learning Rateo�::i�        )�P	����Z1�A$*


Loss/trainD;B��):       ��2	����Z1�A$*

Loss/valW`�A����#       ��wC	����Z1�A$*


Learning Rateo�:b�`N        )�P	 3�Z1�A%*


Loss/train��<B�'�f       ��2	 3�Z1�A%*

Loss/val�S�A�-a#       ��wC	 3�Z1�A%*


Learning Rateo�:C��o        )�P	߳��Z1�A&*


Loss/train��8B���)       ��2	߳��Z1�A&*

Loss/val���A
�g�#       ��wC	߳��Z1�A&*


Learning Rateo�:a��        )�P	�
�Z1�A'*


Loss/train^d:B�%[�       ��2	�
�Z1�A'*

Loss/val�>�AxV�3#       ��wC	�
�Z1�A'*


Learning Rateo�:�h�        )�P	z�x�Z1�A(*


Loss/train�;6B���       ��2	z�x�Z1�A(*

Loss/val&�A�C�$#       ��wC	z�x�Z1�A(*


Learning Rateo�:�D        )�P	P���Z1�A)*


Loss/train�]1B�FN%       ��2	P���Z1�A)*

Loss/val
[[A�Đ�#       ��wC	P���Z1�A)*


Learning Rateo�:_��        )�P	>yZ�Z1�A**


Loss/trainB�/B���       ��2	>yZ�Z1�A**

Loss/val'�NAUW�O#       ��wC	>yZ�Z1�A**


Learning Rateo�:��%�        )�P	����Z1�A+*


Loss/train��0BO�H       ��2	����Z1�A+*

Loss/valoCA��,(#       ��wC	����Z1�A+*


Learning Rateo�:���R        )�P	��5�Z1�A,*


Loss/train��/B.�&�       ��2	��5�Z1�A,*

Loss/valu�A*��#       ��wC	��5�Z1�A,*


Learning Rateo�:�	        )�P	Kg��Z1�A-*


Loss/train��*B+��       ��2	Kg��Z1�A-*

Loss/val��AL��y#       ��wC	Kg��Z1�A-*


Learning Rateo�:�;*�        )�P	'K�Z1�A.*


Loss/train��&B�>�       ��2	'K�Z1�A.*

Loss/val�qA��@#       ��wC	'K�Z1�A.*


Learning Rateo�:p���        )�P	��|�Z1�A/*


Loss/train �'B��ǐ       ��2	��|�Z1�A/*

Loss/val��|A�HY#       ��wC	��|�Z1�A/*


Learning Rateo�:�O�m        )�P	����Z1�A0*


Loss/trainr� Bu}])       ��2	����Z1�A0*

Loss/val|&Ae�M�#       ��wC	����Z1�A0*


Learning Rateo�: l�        )�P	�U�Z1�A1*


Loss/train��%B�C`       ��2	�U�Z1�A1*

Loss/val�,Aá�a#       ��wC	�U�Z1�A1*


Learning Rateo�:�v��