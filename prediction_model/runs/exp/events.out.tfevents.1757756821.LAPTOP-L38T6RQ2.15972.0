H       ��H�	��neO1�A
brain.Event:2R.
,tensorboard.summary.writer.event_file_writer���       ��2	�#GfO1�A*


Loss/train�[Fp��       ���	�#GfO1�A*

Loss/valt��EESJ|!       {��	�#GfO1�A*


Learning Rateo�:9N�u        )�P	��fO1�A*


Loss/train���E7�[8       ��2	��fO1�A*

Loss/valb3�E}�Z�#       ��wC	��fO1�A*


Learning Rateo�:�5[X        )�P	@_gO1�A*


Loss/train�EXf)�       ��2	@_gO1�A*

Loss/valaE����#       ��wC	@_gO1�A*


Learning Rateo�:

9�        )�P	�*�gO1�A*


Loss/trainW�7E0�/�       ��2	�*�gO1�A*

Loss/valll E}���#       ��wC	�*�gO1�A*


Learning Rateo�:N��        )�P	b-whO1�A*


Loss/train�/ES�N�       ��2	�=whO1�A*

Loss/val���D��_�#       ��wC	�=whO1�A*


Learning Rateo�:��T�        )�P	(�iO1�A*


Loss/train��D�       ��2	(�iO1�A*

Loss/val�o�D ��a#       ��wC	(�iO1�A*


Learning Rateo�:z:�        )�P	�1�iO1�A*


Loss/trainrA�D��S       ��2	�1�iO1�A*

Loss/val�S�D�s��#       ��wC	�1�iO1�A*


Learning Rateo�:��        )�P	l+!jO1�A*


Loss/train�f�D�42�       ��2	l+!jO1�A*

Loss/val)܋D�r��#       ��wC	l+!jO1�A*


Learning Rateo�:�mf        )�P	m!�jO1�A*


Loss/train�{D�2g       ��2	m!�jO1�A*

Loss/val*�yD(c�T#       ��wC	m!�jO1�A*


Learning Rateo�:�(�        )�P	-kO1�A	*


Loss/train��bDp�@�       ��2	-kO1�A	*

Loss/val6�aDf�
P#       ��wC	-kO1�A	*


Learning Rateo�:��-        )�P	��lO1�A
*


Loss/train�CQD?f       ��2	��lO1�A
*

Loss/val4j[DօR#       ��wC	��lO1�A
*


Learning Rateo�:4���        )�P	͐�lO1�A*


Loss/trainOdFD�m�'       ��2	͐�lO1�A*

Loss/valY5HD/��y#       ��wC	͐�lO1�A*


Learning Rateo�:b��        )�P	��0mO1�A*


Loss/train��:D�a#b       ��2	��0mO1�A*

Loss/val�!<D�l8I#       ��wC	��0mO1�A*


Learning Rateo�:���        )�P	̳;nO1�A
*


Loss/train%�-D��0       ��2	̳;nO1�A
*

Loss/val�6D��5#       ��wC	̳;nO1�A
*


Learning Rateo�:��6c        )�P	�T�nO1�A*


Loss/train� DT�#       ��2	�T�nO1�A*

Loss/val��#D����#       ��wC	�T�nO1�A*


Learning Rateo�:�|��        )�P	�B6oO1�A*


Loss/trainD!5UL       ��2	�B6oO1�A*

Loss/val��D %�#       ��wC	�B6oO1�A*


Learning Rateo�:���-        )�P	�>�oO1�A*


Loss/train��D�_�s       ��2	�>�oO1�A*

Loss/val��"D��]Y#       ��wC	�>�oO1�A*


Learning Rateo�:�r�        )�P	�URpO1�A*


Loss/train/�C�\�G       ��2	�URpO1�A*

Loss/val�D�y��#       ��wC	�URpO1�A*


Learning Rateo�:o�K        )�P	�pO1�A*


Loss/train2�C�AZ       ��2	�pO1�A*

Loss/valW0�C)Yc&#       ��wC	�pO1�A*


Learning Rateo�:��        )�P	�}qO1�A*


Loss/train��C0K_)       ��2	�}qO1�A*

Loss/val]�C�r�#       ��wC	)}qO1�A*


Learning Rateo�:�]kJ        )�P	��rO1�A*


Loss/train��C����       ��2	��rO1�A*

Loss/val\��C���#       ��wC	��rO1�A*


Learning Rateo�:�!��        )�P	�%�rO1�A*


Loss/train���C:h<�       ��2	�%�rO1�A*

Loss/val�S�C>��#       ��wC	�%�rO1�A*


Learning Rateo�:�Mٻ        )�P	I	dsO1�A*


Loss/train���Cf*�6       ��2	I	dsO1�A*

Loss/val��C]�m#       ��wC	I	dsO1�A*


Learning Rateo�:��T�        )�P	��sO1�A*


Loss/train(tC��])       ��2	��sO1�A*

Loss/val��\C�Z.#       ��wC	��sO1�A*


Learning Rateo�:�7�$        )�P	��tO1�A*


Loss/train�VCn�N�       ��2	��tO1�A*

Loss/val��BC�|{q#       ��wC	x �tO1�A*


Learning Rateo�:�{�~        )�P	��2uO1�A*


Loss/train��<C�8�9       ��2	��2uO1�A*

Loss/vallhC�Ij�#       ��wC	��2uO1�A*


Learning Rateo�:w���        )�P	%�uO1�A*


Loss/train4S4C�m��       ��2	%�uO1�A*

Loss/valܔ2C��t#       ��wC	%�uO1�A*


Learning Rateo�:�V�&        )�P	j�0vO1�A*


Loss/train�^"CX�       ��2	j�0vO1�A*

Loss/val�C�Q"�#       ��wC	j�0vO1�A*


Learning Rateo�:�s��        )�P	
��vO1�A*


Loss/train��C�O&       ��2	
��vO1�A*

Loss/valuC�5��#       ��wC	
��vO1�A*


Learning Rateo�:�m�Z        )�P	��#wO1�A*


Loss/train�!�B�-4�       ��2	��#wO1�A*

Loss/val]�B���Y#       ��wC	��#wO1�A*


Learning Rateo�:n���        )�P	:K�wO1�A*


Loss/train+��BD�w       ��2	:K�wO1�A*

Loss/val��BЁ�#       ��wC	:K�wO1�A*


Learning Rateo�:_��        )�P	�#xO1�A*


Loss/train�q�B��P       ��2	�#xO1�A*

Loss/val���Bm��#       ��wC	�#xO1�A*


Learning Rateo�:n^��        )�P	���xO1�A *


Loss/train���B�o�       ��2	���xO1�A *

Loss/valU{�B/e�#       ��wC	���xO1�A *


Learning Rateo�:���        )�P	� yO1�A!*


Loss/train��B3[1       ��2	� yO1�A!*

Loss/val���B���v#       ��wC	� yO1�A!*


Learning Rateo�:i���        )�P	x��yO1�A"*


Loss/train��B�e�       ��2	x��yO1�A"*

Loss/valwM�Bʘ�#       ��wC	x��yO1�A"*


Learning Rateo�:é�        )�P	'zO1�A#*


Loss/trainL_�B6$       ��2	'zO1�A#*

Loss/val�O{BjO�#       ��wC	'zO1�A#*


Learning Rateo�:�b$        )�P	��zO1�A$*


Loss/train|�zB��i6       ��2	��zO1�A$*

Loss/valN0�BȬ�#       ��wC	��zO1�A$*


Learning Rateo�:�a        )�P	�{O1�A%*


Loss/train[�bB�Y �       ��2	�{O1�A%*

Loss/val��}BJ�,�#       ��wC	�{O1�A%*


Learning Rateo�::���        )�P	�{O1�A&*


Loss/trainW�MB����       ��2	�{O1�A&*

Loss/val��}BA=��#       ��wC	�{O1�A&*


Learning Rateo�:�z�        )�P	-��{O1�A'*


Loss/train��IB�{2�       ��2	���{O1�A'*

Loss/valޗB���#       ��wC	���{O1�A'*


Learning Rateo�:�3f�        )�P	��u|O1�A(*


Loss/train�EBczJt       ��2	��u|O1�A(*

Loss/valC�CBce�K#       ��wC	��u|O1�A(*


Learning Rateo�:�x��        )�P	�d�|O1�A)*


Loss/train�2"B���       ��2	�d�|O1�A)*

Loss/val*tHB�
p�#       ��wC	�d�|O1�A)*


Learning Rateo�:brc        )�P	Xwj}O1�A**


Loss/trainP�B\D�       ��2	Xwj}O1�A**

Loss/val��9B�ส#       ��wC	Xwj}O1�A**


Learning Rateo�:!�
        )�P	O��}O1�A+*


Loss/train�JB�h��       ��2	O��}O1�A+*

Loss/val}W-B��{�#       ��wC	O��}O1�A+*


Learning Rateo�:vܔ�        )�P	r�^~O1�A,*


Loss/trainI�OB�y�/       ��2	r�^~O1�A,*

Loss/valX�lBTB�#       ��wC	r�^~O1�A,*


Learning Rateo�:���`        )�P	y.�~O1�A-*


Loss/train��B͂@U       ��2	y.�~O1�A-*

Loss/valj7BR�O#       ��wC	y.�~O1�A-*


Learning Rateo�:e�l        )�P	��XO1�A.*


Loss/train�0�A�r��       ��2	��XO1�A.*

Loss/vala�B{�`#       ��wC	��XO1�A.*


Learning Rateo�:��`�        )�P	9^�O1�A/*


Loss/trainm�B��?p       ��2	9^�O1�A/*

Loss/val�_�A�T�b#       ��wC	9^�O1�A/*


Learning Rateo�:��9        )�P	��L�O1�A0*


Loss/train�C�A���       ��2	��L�O1�A0*

Loss/val)�B+�#       ��wC	��L�O1�A0*


Learning Rateo�:��        )�P	+�ƀO1�A1*


Loss/train�Bg%�e       ��2	+�ƀO1�A1*

Loss/val};B�Y��#       ��wC	+�ƀO1�A1*


Learning Rateo�:f�>�