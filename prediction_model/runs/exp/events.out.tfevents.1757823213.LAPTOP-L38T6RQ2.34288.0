H       ��H�	/�Y;�1�A
brain.Event:2R.
,tensorboard.summary.writer.event_file_writer�       ��2	�(<�1�A*


Loss/train��Erv�G       ���	�(<�1�A*

Loss/val�vE�)��!       {��	�(<�1�A*


Learning Rateo�:7�q�        )�P	�)�<�1�A*


Loss/train+9?E�_��       ��2	�)�<�1�A*

Loss/valHEdq#       ��wC	�)�<�1�A*


Learning Rateo�:�۠^        )�P	L�=�1�A*


Loss/traina)�DpG97       ��2	L�=�1�A*

Loss/valCh�Dv�sx#       ��wC	L�=�1�A*


Learning Rateo�:�N�        )�P	��v=�1�A*


Loss/train;;]D�\�       ��2	��v=�1�A*

Loss/val4cD@�5#       ��wC	��v=�1�A*


Learning Rateo�:���        )�P	WD�=�1�A*


Loss/train��Cr_,�       ��2	WD�=�1�A*

Loss/val���C涽�#       ��wC	WD�=�1�A*


Learning Rateo�:L ��        )�P	�X[>�1�A*


Loss/train�РC֒'K       ��2	�X[>�1�A*

Loss/valq�gCB�5�#       ��wC	�X[>�1�A*


Learning Rateo�:�a9c        )�P	�L�>�1�A*


Loss/train��lC`��x       ��2	�L�>�1�A*

Loss/val��3Cz���#       ��wC	�L�>�1�A*


Learning Rateo�:|�X        )�P	�eC?�1�A*


Loss/train��@CV�]�       ��2	�eC?�1�A*

Loss/val� C�{�#       ��wC	�eC?�1�A*


Learning Rateo�:n��X        )�P	a�?�1�A*


Loss/train\�$CE��       ��2	a�?�1�A*

Loss/val\B Cq�+#       ��wC	a�?�1�A*


Learning Rateo�:3��        )�P	�-@�1�A	*


Loss/train2�Cb�Lu       ��2	�-@�1�A	*

Loss/val|��B�_�{#       ��wC	�-@�1�A	*


Learning Rateo�:�(G�        )�P	�ܷ@�1�A
*


Loss/train�	C.���       ��2	�ܷ@�1�A
*

Loss/val'��B�<��#       ��wC	�ܷ@�1�A
*


Learning Rateo�:An�"        )�P	A�ZA�1�A*


Loss/train�8�B*���       ��2	A�ZA�1�A*

Loss/valį�B�h#       ��wC	A�ZA�1�A*


Learning Rateo�:�y�        )�P	'�B�1�A*


Loss/train�(�Bl�Mh       ��2	'�B�1�A*

Loss/val�|�B)���#       ��wC	'�B�1�A*


Learning Rateo�:�gf9        )�P	�_�B�1�A
*


Loss/trainC��B��*W       ��2	�_�B�1�A
*

Loss/val7G�Bl�3&#       ��wC	�_�B�1�A
*


Learning Rateo�:o��]        )�P	+�
C�1�A*


Loss/train��B��U�       ��2	+�
C�1�A*

Loss/val�:�BO�/#       ��wC	+�
C�1�A*


Learning Rateo�:�KĮ        )�P	R��C�1�A*


Loss/train�)�B*��U       ��2	R��C�1�A*

Loss/val-^�B�u��#       ��wC	R��C�1�A*


Learning Rateo�:��r        )�P	�uD�1�A*


Loss/trainVs�B�9�A       ��2	�uD�1�A*

Loss/val6��B��S#       ��wC	�uD�1�A*


Learning Rateo�:����        )�P	�b�D�1�A*


Loss/train���B�:��       ��2	2s�D�1�A*

Loss/val��_B�W0#       ��wC	2s�D�1�A*


Learning Rateo�:���        )�P	T�%E�1�A*


Loss/trainsͥB���{       ��2	T�%E�1�A*

Loss/valо�B=Z�#       ��wC	T�%E�1�A*


Learning Rateo�:}6�E        )�P	��E�1�A*


Loss/trainJ�B?'d�       ��2	��E�1�A*

Loss/val·RB�k� #       ��wC	��E�1�A*


Learning Rateo�:R3>Z        )�P	�M9F�1�A*


Loss/train@��B2dޮ       ��2	�M9F�1�A*

Loss/val��;B���#       ��wC	�M9F�1�A*


Learning Rateo�:k:�        )�P	Ӷ�F�1�A*


Loss/traino��BWY��       ��2	Ӷ�F�1�A*

Loss/vali#B�;��#       ��wC	Ӷ�F�1�A*


Learning Rateo�:-�g�        )�P	�@CG�1�A*


Loss/train�ǆB�f��       ��2	�@CG�1�A*

Loss/valJB���'#       ��wC	�@CG�1�A*


Learning Rateo�:�8�{        )�P	+*�G�1�A*


Loss/train�ւB�0�Z       ��2	+*�G�1�A*

Loss/val!
BI��<#       ��wC	+*�G�1�A*


Learning Rateo�:;�p�        )�P	��5H�1�A*


Loss/train��{B�P       ��2	��5H�1�A*

Loss/valVhB|8�:#       ��wC	��5H�1�A*


Learning Rateo�:�3f�        )�P	��H�1�A*


Loss/train�vB�s       ��2	��H�1�A*

Loss/valT��A�v�&#       ��wC	��H�1�A*


Learning Rateo�:�H�{        )�P	f�I�1�A*


Loss/train��mB4�E�       ��2	f�I�1�A*

Loss/valQ�AZ��#       ��wC	f�I�1�A*


Learning Rateo�:�P�        )�P	a��I�1�A*


Loss/train��[B̪��       ��2	a��I�1�A*

Loss/val��A�y-�#       ��wC	a��I�1�A*


Learning Rateo�:�6cE        )�P	 �J�1�A*


Loss/train��hB���z       ��2	 �J�1�A*

Loss/valqC�A�;R�#       ��wC	 �J�1�A*


Learning Rateo�:]9�        )�P	r�rJ�1�A*


Loss/trainX�SB��e       ��2	r�rJ�1�A*

Loss/val�JBS��N#       ��wC	r�rJ�1�A*


Learning Rateo�:/�        )�P	�?�J�1�A*


Loss/train	WBb�ն       ��2	�?�J�1�A*

Loss/val��A [��#       ��wC	�?�J�1�A*


Learning Rateo�:v�/�        )�P	��WK�1�A*


Loss/train��PB9E       ��2	C�WK�1�A*

Loss/valWܹAH/�c#       ��wC	C�WK�1�A*


Learning Rateo�:[        )�P	4y�K�1�A *


Loss/trainjnLB
       ��2	4y�K�1�A *

Loss/valCF�A��ջ#       ��wC	4y�K�1�A *


Learning Rateo�:�
�        )�P	c�:L�1�A!*


Loss/train��LB䔹       ��2	c�:L�1�A!*

Loss/val�3�A����#       ��wC	c�:L�1�A!*


Learning Rateo�: i��        )�P	�B�L�1�A"*


Loss/trainH�KB�Ȑ+       ��2	�B�L�1�A"*

Loss/valZ��A��#
#       ��wC	�B�L�1�A"*


Learning Rateo�:Ք�7        )�P	έM�1�A#*


Loss/train��?B���       ��2	έM�1�A#*

Loss/val8�A.i }#       ��wC	έM�1�A#*


Learning Rateo�:�'�        )�P	���M�1�A$*


Loss/trainU�9B�Hm       ��2	���M�1�A$*

Loss/valF׋A�ǳ#       ��wC	���M�1�A$*


Learning Rateo�:ٖ*�        )�P	�N�1�A%*


Loss/train�@Bp�o�       ��2	�N�1�A%*

Loss/val��AF�#       ��wC	�N�1�A%*


Learning Rateo�:R�Q-        )�P	yN�1�A&*


Loss/train��;B�RJ       ��2	yN�1�A&*

Loss/val��Aw�#       ��wC	yN�1�A&*


Learning Rateo�:���        )�P	?�N�1�A'*


Loss/traing�3B3Pf�       ��2	?�N�1�A'*

Loss/val:�xA��C+#       ��wC	?�N�1�A'*


Learning Rateo�:�C�r        )�P	5!]O�1�A(*


Loss/train\�6B�*�*       ��2	�1]O�1�A(*

Loss/val�TqA���#       ��wC	�1]O�1�A(*


Learning Rateo�:D��        )�P	'��O�1�A)*


Loss/trainij0BP���       ��2	'��O�1�A)*

Loss/val]%iAh�[�#       ��wC	'��O�1�A)*


Learning Rateo�:��A�        )�P	��LP�1�A**


Loss/train&\+B�!�       ��2	��LP�1�A**

Loss/val�	dA7�տ#       ��wC	��LP�1�A**


Learning Rateo�:���        )�P	w�P�1�A+*


Loss/train҆+B6���       ��2	w�P�1�A+*

Loss/val���Aem��#       ��wC	w�P�1�A+*


Learning Rateo�:q�(�        )�P	�u0Q�1�A,*


Loss/train=A+B�41K       ��2	�u0Q�1�A,*

Loss/val��]AdK��#       ��wC	�u0Q�1�A,*


Learning Rateo�:^v�J        )�P	�\�Q�1�A-*


Loss/train��&B91��       ��2	�\�Q�1�A-*

Loss/valdo^A�N�l#       ��wC	�\�Q�1�A-*


Learning Rateo�:��<        )�P	�SR�1�A.*


Loss/trainoQ(B����       ��2	�SR�1�A.*

Loss/val�RA��g5#       ��wC	�SR�1�A.*


Learning Rateo�:
��        )�P	K/�R�1�A/*


Loss/train�-B@�m�       ��2	K/�R�1�A/*

Loss/valRg�A5֮#       ��wC	K/�R�1�A/*


Learning Rateo�:!6]        )�P	�$�R�1�A0*


Loss/train�#&B��!\       ��2	�$�R�1�A0*

Loss/val��;A��G�#       ��wC	�$�R�1�A0*


Learning Rateo�:䑔�        )�P	�nS�1�A1*


Loss/train`�'B��v       ��2	�nS�1�A1*

Loss/valR�-AQLS#       ��wC	�nS�1�A1*


Learning Rateo�:�"�v