import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
import numpy as np
import os
import time
from data_loader import get_data_loaders
from LSTM_models import LSTMRULPredictor


def train_model(data_path, subset='FD001', window_size=30, hidden_size=64, num_layers=2,
                batch_size=32, num_epochs=100, learning_rate=0.001, log_dir='runs/exp'):
    """训练LSTM模型"""
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # 创建数据加载器
    train_loader, val_loader = get_data_loaders(data_path, subset, batch_size, window_size)

    # 初始化模型
    input_size = train_loader.dataset[0][0].shape[1]  # 特征数量
    model = LSTMRULPredictor(input_size, hidden_size, num_layers, output_size=1)
    model.to(device)

    # 定义损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5)

    # 早停参数
    early_stopping_patience = 15
    best_val_loss = float('inf')
    epochs_no_improve = 0

    # 创建日志目录
    os.makedirs(log_dir, exist_ok=True)
    writer = SummaryWriter(log_dir)

    # 训练循环
    best_val_loss = float('inf')
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(device), target.to(device)

            optimizer.zero_grad()
            output = model(data)
            loss = criterion(output, target)
            loss.backward()
            optimizer.step()

            train_loss += loss.item()

            if batch_idx % 100 == 0:
                print(f'Epoch: {epoch} [{batch_idx * len(data)}/{len(train_loader.dataset)} '
                      f'({100. * batch_idx / len(train_loader):.0f}%)]\tLoss: {loss.item():.6f}')

        # 验证阶段
        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            for data, target in val_loader:
                data, target = data.to(device), target.to(device)
                output = model(data)
                val_loss += criterion(output, target).item()

        train_loss /= len(train_loader)
        val_loss /= len(val_loader)

        # 学习率调整
        scheduler.step(val_loss)

        # 记录到TensorBoard
        writer.add_scalar('Loss/train', train_loss, epoch)
        writer.add_scalar('Loss/val', val_loss, epoch)
        writer.add_scalar('Learning Rate', optimizer.param_groups[0]['lr'], epoch)

        print(f'Epoch {epoch}: Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}')

        # 保存最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            epochs_no_improve = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'loss': val_loss,
            }, f'{log_dir}/best_model.pth')
        else:
            epochs_no_improve += 1
            if epochs_no_improve >= early_stopping_patience:
                print(f"Early stopping at epoch {epoch}")
                break

    writer.close()
    print(f"Training completed. Best validation loss: {best_val_loss:.6f}")


if __name__ == "__main__":
    data_path = "./data/C-MAPSS-Data"
    train_model(data_path, subset='FD001', num_epochs=50)